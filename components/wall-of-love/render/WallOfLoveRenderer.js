import dynamic from 'next/dynamic';
import isRtlText from 'is-rtl-text';
import { useState } from 'react';
import { Menu, X } from 'lucide-react';
import Head from 'next/head';
import useWindowWidth from '../../../lib/useWindowWidth';
import Grid from '../../widgets/render/Grid';
import TotalReviewsHeader from '../../widgets/TotalReviewsHeader';

const Branding = dynamic(() => import('../../widgets/render/Branding'));

function WallOfLove({ testimonials, wallOfLove, preview, publicId, hasNextPage, totals, previewParams }) {
  const [showMobileMenu, setShowMobileMenu] = useState(false);
  const winWidth = useWindowWidth();

  return (
    <>
      <Head>
        <link
          href={`https://fonts.bunny.net/css2?family=${(wallOfLove?.design?.font || 'Nunito').replace(/ /g, '+')}:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700&display=swap`}
          rel="stylesheet"
        />
      </Head>
      <div className="min-h-screen relative pb-10" style={{ backgroundColor: wallOfLove?.design?.backgroundColor || '#fcfcfc', fontFamily: wallOfLove?.design?.font }}>
        <div className="min-h-full mx-auto ">
          <div className="h-full mx-auto" style={{ backgroundColor: wallOfLove?.hero?.backgroundColor || '#ffffff' }}>
            <div className="w-full container mx-auto">
              <div className="flex flex-col " style={{ color: wallOfLove?.hero?.textColor }}>
                <div className="flex items-center w-full pl-3">

                  {wallOfLove?.design?.showLogo && wallOfLove?.design?.logo && (
                  <div className="self-center">
                    {wallOfLove?.design?.logoLink ? (
                      <a
                        className="hover:opacity-80"
                        href={wallOfLove?.design?.logoLink}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <img
                          alt="hero picture"
                          style={{
                            maxHeight: `${wallOfLove?.design?.logoSize}px`,
                            height: `${wallOfLove?.design?.logoSize}px`,
                            maxWidth: `${wallOfLove?.design?.logoSize * 2}px`,
                            objectFit: 'contain',
                          }}
                          className={'p-2'}
                          src={wallOfLove?.design?.logo}
                        />
                      </a>
                    ) : (
                      <img
                        alt="hero picture"
                        style={{
                          maxHeight: `${wallOfLove?.design?.logoSize}px`,
                          height: `${wallOfLove?.design?.logoSize}px`,
                          maxWidth: `${wallOfLove?.design?.logoSize * 2}px`,
                          objectFit: 'contain',
                        }}
                        className={'p-2'}
                        src={wallOfLove?.design?.logo}
                      />
                    )}
                  </div>
                  )}

                  {
                                        wallOfLove?.navigation?.buttons.length > 0 && (
                                        <>
                                          <div onClick={() => setShowMobileMenu(!showMobileMenu)} className={`${showMobileMenu ? 'block' : 'hidden'} xl:hidden w-full h-full fixed top-0 left-0 bg-black opacity-75 z-50`} />
                                          <div className="block lg:hidden flex items-center flex flex-grow justify-end ">
                                            <button
                                              className="outline-none focus:outline-none hover:outline-none p-4"
                                              onClick={() => setShowMobileMenu(!showMobileMenu)}
                                            >
                                              <Menu size={32} />
                                            </button>
                                          </div>
                                          <div
                                            style={{ backgroundColor: wallOfLove?.hero?.backgroundColor }}
                                            className={` ${showMobileMenu ? 'shadow-2xl' : 'translate-x-full'} block xl:hidden z-50 xl:z-auto sidebar  w-72 xl:w-64 py-1 px-2 xl:py-0 xl:px-0 fixed inset-y-0 right-0 transform  xl:-translate-x-0 transition duration-200 ease-in-out`}
                                          >
                                            <div className="flex items-center pb-4">
                                              <div
                                                className="space-x-4 p-2 lg:mt-1 flex items-center justify-center mx-auto"
                                              />
                                              <button
                                                className="absolute right-3 top-4 xl:hidden"
                                                style={{ color: wallOfLove?.hero?.textColor }}

                                                onClick={() => setShowMobileMenu(!showMobileMenu)}
                                              >
                                                <X color={wallOfLove?.hero?.textColor} size={30} />
                                              </button>
                                            </div>
                                            {showMobileMenu && (
                                            <div className="flex flex-col justify-end space-y-7 mt-5">
                                              {wallOfLove?.navigation?.buttons && wallOfLove?.navigation?.buttons.map((button, idx) => (
                                                <div key={idx} className={`ml-5 font-bold p-1 ${isRtlText(button.text) ? 'hebrew-font direction-rtl' : ''}`}>
                                                  <a target="_blank" href={button.url} rel="noopener">
                                                    {button.text}
                                                  </a>
                                                </div>
                                              ))}
                                            </div>
                                            )}

                                          </div>
                                          <div className={`flex-grow flex items-center justify-end w-20 h-20 hidden lg:flex ${preview && 'mr-5'}`}>
                                            {wallOfLove?.navigation?.buttons && wallOfLove?.navigation?.buttons.map((button, idx) => (
                                              <div key={idx} className={`ml-5 font-bold p-1 ${isRtlText(button.text) ? 'hebrew-font direction-rtl' : ''}`}>
                                                <a target="_blank" className="hover:opacity-70" href={button.url} rel="noopener">
                                                  {button.text}
                                                </a>
                                              </div>
                                            ))}
                                          </div>
                                        </>
                                        )
                                    }

                </div>
                <div
                  className={'px-4 pb-20 sm:pb-40 lg:pb-48 mx-auto sm:max-w-xl md:max-w-full lg:max-w-screen-xl md:px-12 lg:px-4 pt-12'}
                >
                  <div className="max-w-xl sm:mx-auto lg:max-w-2xl">
                    <div className="flex flex-col mb-16 text-center sm:mb-0">
                      <div className="max-w-xl mb-10 md:mx-auto text-center lg:max-w-2xl md:mb-12">
                        {
                                                  wallOfLove?.settings?.showTotals
                                                  && (
                                                  <div className="inline-flex mb-7">
                                                    <TotalReviewsHeader totals={totals} widget={wallOfLove} compact />
                                                  </div>
                                                  )
                                                }

                        <h2 className={`max-w-lg mb-5 ${isRtlText(wallOfLove?.hero?.title) ? 'hebrew-font direction-rtl' : ''} text-3xl font-extrabold leading-none tracking-tight sm:text-4xl md:mx-auto`}>
                          {wallOfLove?.hero?.title}
                        </h2>
                        <p className={`text-base md:text-xl max-w-xl font-medium ${isRtlText(wallOfLove?.hero?.text) ? 'hebrew-font direction-rtl' : ''}`}>
                          {wallOfLove?.hero?.text}
                        </p>
                      </div>
                      {wallOfLove?.hero?.cta?.active && (
                      <div>
                        <a
                          href={wallOfLove?.hero?.cta?.url}
                          target={'_blank'}
                          className={`inline-flex items-center justify-center h-12 text-base md:text-lg px-6 font-bold rounded-full hover:shadow-lg hover:opacity-90 ${isRtlText(wallOfLove?.hero?.cta?.title) ? 'hebrew-font direction-rtl' : ''}`}
                          style={{
                            backgroundColor: wallOfLove?.hero?.cta?.buttonColor || '#fb285a',
                            color: wallOfLove?.hero?.cta?.textColor || '#ffffff',
                          }}
                          rel="noopener"
                        >
                          {wallOfLove?.hero?.cta?.title}
                        </a>
                      </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

            </div>
          </div>
          <div className="container mx-auto ">
            <div className={`w-full mx-auto text-white -mt-24 ${winWidth && winWidth > 0 && 'opacity-100'}`}>
              <Grid preview={preview} hasNextPage={hasNextPage} widget={wallOfLove} testimonials={testimonials} wallOfLove wallOfLovePublicId={publicId} previewParams={previewParams} />
            </div>
          </div>

          <div className="relative z-50">
            <div className={`fixed ${preview ? 'bottom-0 right-12' : 'bottom-0 right-5'}`}>
              {!wallOfLove?.settings?.hideBranding && <Branding source={'walloflove'} />}
            </div>
          </div>
        </div>
      </div>
    </>
  );
}

export default WallOfLove;
